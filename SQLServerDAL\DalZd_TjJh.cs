﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_TjJh.cs
*
* 功 能： N/A
* 类 名： DalZd_TjJh
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:20   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_TjJh
	/// </summary>
	public partial class DalZd_TjJh : IDalZd_TjJh
	{
		public DalZd_TjJh()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Tj1_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_TjJh");
			strSql.Append(" where Tj1_Code=@Tj1_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj1_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Tj1_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_TjJh model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_TjJh(");
			strSql.Append("Tj1_Code,Tj1_Name,Tj1_Jd,Tj1_Bm,Tj1_Yljg,Tj1_Rs,Tj1_Memo,Tj1_Date,Jsr_Code,Jsr_Name,Lr_Date)");
			strSql.Append(" values (");
			strSql.Append("@Tj1_Code,@Tj1_Name,@Tj1_Jd,@Tj1_Bm,@Tj1_Yljg,@Tj1_Rs,@Tj1_Memo,@Tj1_Date,@Jsr_Code,@Jsr_Name,@Lr_Date)");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj1_Code", SqlDbType.Char,10),
					new SqlParameter("@Tj1_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Tj1_Jd", SqlDbType.VarChar,50),
					new SqlParameter("@Tj1_Bm", SqlDbType.VarChar,50),
					new SqlParameter("@Tj1_Yljg", SqlDbType.VarChar,50),
					new SqlParameter("@Tj1_Rs", SqlDbType.Int,4),
					new SqlParameter("@Tj1_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Tj1_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Lr_Date", SqlDbType.DateTime)};
			parameters[0].Value = model.Tj1_Code;
			parameters[1].Value = model.Tj1_Name;
			parameters[2].Value = model.Tj1_Jd;
			parameters[3].Value = model.Tj1_Bm;
			parameters[4].Value = model.Tj1_Yljg;
			parameters[5].Value = model.Tj1_Rs;
			parameters[6].Value = model.Tj1_Memo;
			parameters[7].Value = model.Tj1_Date;
			parameters[8].Value = model.Jsr_Code;
			parameters[9].Value = model.Jsr_Name;
			parameters[10].Value = model.Lr_Date;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_TjJh model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_TjJh set ");
			strSql.Append("Tj1_Name=@Tj1_Name,");
			strSql.Append("Tj1_Jd=@Tj1_Jd,");
			strSql.Append("Tj1_Bm=@Tj1_Bm,");
			strSql.Append("Tj1_Yljg=@Tj1_Yljg,");
			strSql.Append("Tj1_Rs=@Tj1_Rs,");
			strSql.Append("Tj1_Memo=@Tj1_Memo,");
			strSql.Append("Tj1_Date=@Tj1_Date,");
			strSql.Append("Jsr_Code=@Jsr_Code,");
			strSql.Append("Jsr_Name=@Jsr_Name,");
			strSql.Append("Lr_Date=@Lr_Date");
			strSql.Append(" where Tj1_Code=@Tj1_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj1_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Tj1_Jd", SqlDbType.VarChar,50),
					new SqlParameter("@Tj1_Bm", SqlDbType.VarChar,50),
					new SqlParameter("@Tj1_Yljg", SqlDbType.VarChar,50),
					new SqlParameter("@Tj1_Rs", SqlDbType.Int,4),
					new SqlParameter("@Tj1_Memo", SqlDbType.VarChar,200),
					new SqlParameter("@Tj1_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Jsr_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Jsr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Lr_Date", SqlDbType.DateTime),
					new SqlParameter("@Tj1_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Tj1_Name;
			parameters[1].Value = model.Tj1_Jd;
			parameters[2].Value = model.Tj1_Bm;
			parameters[3].Value = model.Tj1_Yljg;
			parameters[4].Value = model.Tj1_Rs;
			parameters[5].Value = model.Tj1_Memo;
			parameters[6].Value = model.Tj1_Date;
			parameters[7].Value = model.Jsr_Code;
			parameters[8].Value = model.Jsr_Name;
			parameters[9].Value = model.Lr_Date;
			parameters[10].Value = model.Tj1_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Tj1_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_TjJh ");
			strSql.Append(" where Tj1_Code=@Tj1_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj1_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Tj1_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Tj1_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_TjJh ");
			strSql.Append(" where Tj1_Code in (" + Tj1_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_TjJh GetModel(string Tj1_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Tj1_Code,Tj1_Name,Tj1_Jd,Tj1_Bm,Tj1_Yljg,Tj1_Rs,Tj1_Memo,Tj1_Date,Jsr_Code,Jsr_Name,Lr_Date from Zd_TjJh ");
			strSql.Append(" where Tj1_Code=@Tj1_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj1_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Tj1_Code;

			Model.MdlZd_TjJh model = new Model.MdlZd_TjJh();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_TjJh DataRowToModel(DataRow row)
		{
			Model.MdlZd_TjJh model = new Model.MdlZd_TjJh();
			if (row != null)
			{
				if (row["Tj1_Code"] != null)
				{
					model.Tj1_Code = row["Tj1_Code"].ToString();
				}
				if (row["Tj1_Name"] != null)
				{
					model.Tj1_Name = row["Tj1_Name"].ToString();
				}
				if (row["Tj1_Jd"] != null)
				{
					model.Tj1_Jd = row["Tj1_Jd"].ToString();
				}
				if (row["Tj1_Bm"] != null)
				{
					model.Tj1_Bm = row["Tj1_Bm"].ToString();
				}
				if (row["Tj1_Yljg"] != null)
				{
					model.Tj1_Yljg = row["Tj1_Yljg"].ToString();
				}
				if (row["Tj1_Rs"] != null && row["Tj1_Rs"].ToString() != "")
				{
					model.Tj1_Rs = int.Parse(row["Tj1_Rs"].ToString());
				}
				if (row["Tj1_Memo"] != null)
				{
					model.Tj1_Memo = row["Tj1_Memo"].ToString();
				}
				if (row["Tj1_Date"] != null && row["Tj1_Date"].ToString() != "")
				{
					model.Tj1_Date = DateTime.Parse(row["Tj1_Date"].ToString());
				}
				if (row["Jsr_Code"] != null)
				{
					model.Jsr_Code = row["Jsr_Code"].ToString();
				}
				if (row["Jsr_Name"] != null)
				{
					model.Jsr_Name = row["Jsr_Name"].ToString();
				}
				if (row["Lr_Date"] != null && row["Lr_Date"].ToString() != "")
				{
					model.Lr_Date = DateTime.Parse(row["Lr_Date"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Tj1_Code,Tj1_Name,Tj1_Jd,Tj1_Bm,Tj1_Yljg,Tj1_Rs,Tj1_Memo,Tj1_Date,Jsr_Code,Jsr_Name,Lr_Date ");
			strSql.Append(" FROM Zd_TjJh ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Tj1_Code,Tj1_Name,Tj1_Jd,Tj1_Bm,Tj1_Yljg,Tj1_Rs,Tj1_Memo,Tj1_Date,Jsr_Code,Jsr_Name,Lr_Date ");
			strSql.Append(" FROM Zd_TjJh ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_TjJh ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Tj1_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_TjJh T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_TjJh";
			parameters[1].Value = "Tj1_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = DateTime.Now.ToString("yyMMdd") + Common.WinFormVar.Var.DbHelper.F_MaxCode($"SELECT MAX(RIGHT(Tj1_Code,4)) FROM Zd_TjJh WHERE LEFT(Tj1_Code,6)='" + DateTime.Now.ToString("yyMMdd") + "'", length - 6);
			return max;
		}

		#endregion  ExtensionMethod
	}
}

