using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using YdPublicFunction;

namespace YdGSP
{
    public partial class Zd_TjJh1 : Common.BaseForm.BaseDict1
    {
        private BLL.BllZd_TjJh _bllZdTjJh = new BllZd_TjJh();
        public Zd_TjJh1()
        {
            InitializeComponent();
        }

        private void Zd_TjJh1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            FormInit();
            DataInit();
            myGrid1.Select();
        }
        #region 初始化
        private void FormInit()
        {
            doubleDateEdit1.CustomFormat = "yyyy-MM-dd";
            doubleDateEdit1.DisplayFormat = "yyyy-MM-dd";
            doubleDateEdit1.EditFormat = "yyyy-MM-dd";
            doubleDateEdit1.SelectedIndex = 5;

            myGrid1.Init_Grid();
            myGrid1.Init_Column("体检编码", "Tj1_Code", 120, "中", "", false);
            myGrid1.Init_Column("体检计划名称", "Tj1_Name", 280, "左", "", false);
            myGrid1.Init_Column("体检时间", "Tj1_Jd", 120, "中", "", false);
            myGrid1.Init_Column("部门", "Tj1_Bm", 120, "左", "", false);
            myGrid1.Init_Column("医疗机构", "Tj1_Yljg", 200, "左", "", false);
            myGrid1.Init_Column("人数", "Tj1_Rs", 80, "中", "", false);
            myGrid1.Init_Column("制定计划时间", "Tj1_Date", 180, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("备注", "Tj1_Memo", 200, "左", "", false);
            myGrid1.ColumnFooters = true;
            myGrid1.AllowSort = true;
        }
        #endregion

        #region 自定义函数
        private void DataInit()
        {
            base.MyTable = _bllZdTjJh.GetList($"Tj1_Date between '{DateTime.Parse(doubleDateEdit1.StartValue.ToString()).ToString("yyy-MM-dd")}' " +
                                             $"And '{DateTime.Parse(doubleDateEdit1.EndValue.ToString()).ToString("yyy-MM-dd 23:59:59")}'").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["Tj1_Code"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            myGrid1.BeginInvoke(new Action(() => this.myGrid1.DataTable = base.MyTable));
            base.MyView = (DataView)base.MyCm.List;
            base.MyView.Sort = "Tj1_Date desc";
            DataSum();
        }
        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            Zd_TjJh2 vform = new Zd_TjJh2(base.Insert, base.MyRow, base.MyTable);
            vform.Tag = base.MyRow["Tj1_Code"];
            vform.ZbTransmitTxt = base.MyTransmitTxt;
            base.AddTabControl(vform, "人员体检计划明细-" + (base.MyRow["Tj1_Name"].ToString() == "" ? "新计划" : base.MyRow["Tj1_Name"].ToString()), YdResources.C_Resources.GetImage16(""));
        }
        protected override void DataDelete()
        {
            if (myGrid1.Row + 1 > myGrid1.RowCount)
            {
                MessageBox.Show("请选择体检计划记录!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;

            if (MessageBox.Show("是否删除:人员体检计划【" + base.MyRow["Tj1_Name"] + "】记录?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No) return;

            if (_bllZdTjJh.Delete(base.MyRow["Tj1_Code"].ToString()) == true)
            {
                myGrid1.Delete();
                base.MyTable.AcceptChanges();
                DataSum();
                MessageBox.Show("数据删除成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
        }

        private void DataSum()
        {
            LblTotal.BeginInvoke(new Action(() => this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString()));
        }
        #endregion

        #region 事件
        private void Cmd_Add_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEdit(true);
        }
        private void Cmd_Del_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataDelete();
        }
        private void CmdQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataInit();
        }
        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            char[] split = { ' ' };
            string filter = "Tj1_Name+Tj1_Bm+Tj1_Yljg";
            string strFilter = "";
            foreach (string substr in TxtFilter.Text.Replace("*", "[*]").Replace("%", "[%]").Split(split))
            {
                strFilter = strFilter + filter + " like '*" + substr + "*' And ";
            }
            strFilter = strFilter.Substring(0, strFilter.Length - 5);
            MyView.RowFilter = strFilter;
            DataSum();
        }
        #endregion
    }
}
