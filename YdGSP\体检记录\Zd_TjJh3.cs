using BLL;
using Model;
using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace YdGSP
{
    public partial class Zd_TjJh3 : Common.BaseForm.DoubleFormRK2
    {
        private string tj1_Code;
        private bool _frmInit = false;
        private BllZd_TjJl _bllZdTjJl = new BllZd_TjJl();
        public Zd_TjJh3(string tj1_Code, bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            this.tj1_Code = tj1_Code;
            base.Insert = insert;
            base.SubItemRow = row;
            base.MyTable = table;
            TxtRyName.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Zd_TjJh3_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.SubItemRow);
            _frmInit = true;
        }

        #region 初始化函数
        private void FormInit()
        {
            TxtCode.Enabled = false;

            DtpTjDate.EditFormat = "yyyy-MM-dd HH:mm:ss";
            DtpTjDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DtpTjDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";

            TxtRyName.GotFocus += new System.EventHandler(base.InputCn);
            TxtTjBm.GotFocus += new System.EventHandler(base.InputCn);
            TxtDh.GotFocus += new System.EventHandler(base.InputEn);
            TxtJkzk.GotFocus += new System.EventHandler(base.InputCn);
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);

            panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }
        #endregion

        #region 自定义函数
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllZdTjJl.MaxCode(10);
            TxtRyName.Text = "";
            DtpTjDate.Value = DateTime.Now;
            TxtTjBm.Text = "";
            TxtDh.Text = "";
            TxtJkzk.Text = "";
            TxtMemo.Text = "";

            TxtRyName.Select();
        }
        protected override void DataShow(DataRow row)
        {
            base.Insert = false;

            TxtCode.Text = row["Tj2_Code"] + "";
            TxtRyName.Text = row["Ry_Name"] + "";

            if (row["Tj2_Date"] != DBNull.Value)
                DtpTjDate.Value = Convert.ToDateTime(row["Tj2_Date"]);
            else
                DtpTjDate.Value = DateTime.Now;

            TxtTjBm.Text = row["Tj2_Bm"] + "";
            TxtDh.Text = row["Tj2_Dh"] + "";
            TxtJkzk.Text = row["Tj2_Jkzk"] + "";
            TxtMemo.Text = row["Tj2_Memo"] + "";

            TxtRyName.Select();
        }

        private bool DataCheck()
        {
            if (TxtRyName.Text.Trim() == "")
            {
                MessageBox.Show("人员姓名不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtRyName.Focus();
                return false;
            }
            if (Insert)
            {
                if (MyTable.AsEnumerable().Where(p => p["Ry_Name"].ToString() == TxtRyName.Text.Trim()).Count() > 0)
                {
                    MessageBox.Show($"请勿重复添加此人员 {TxtRyName.Text}!", "提示", MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    TxtRyName.Select();
                    return false;
                }
            }
            else
            {
                if (MyTable.AsEnumerable().Where(p => p["Ry_Name"].ToString() == TxtRyName.Text.Trim() && p["Tj2_Code"].ToString() != TxtCode.Text.Trim()).Count() > 0)
                {
                    MessageBox.Show($"请勿重复添加此人员 {TxtRyName.Text}!", "提示", MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    TxtRyName.Select();
                    return false;
                }
            }

            return true;
        }

        private void DataAdd()
        {
            base.SubItemRow = base.MyTable.NewRow();

            SubItemRow["Tj1_Code"] = tj1_Code;
            SubItemRow["Tj2_Code"] = TxtCode.Text.Trim();
            SubItemRow["Ry_Code"] = "";
            SubItemRow["Ry_Name"] = TxtRyName.Text.Trim();
            SubItemRow["Tj2_Date"] = DtpTjDate.Value;
            SubItemRow["Tj2_Bm"] = TxtTjBm.Text.Trim();
            SubItemRow["Tj2_Dh"] = TxtDh.Text.Trim();
            SubItemRow["Tj2_Jkzk"] = TxtJkzk.Text.Trim();
            SubItemRow["Tj2_Memo"] = TxtMemo.Text.Trim();
            SubItemRow["Tj2_LrDate"] = DateTime.Now;
            SubItemRow["Tj2_Jsr_Code"] = YdVar.Var.JsrCode;
            SubItemRow["Tj2_Jsr_Name"] = YdVar.Var.UserName;
            SubItemRow["Fj_Sl"] = 0;

            //数据保存
            try
            {
                MdlZd_TjJl mdlZdTjJl = Common.DataTableToList.ToModel<MdlZd_TjJl>(SubItemRow);

                _bllZdTjJl.Add(mdlZdTjJl);
                base.MyTable.Rows.Add(base.SubItemRow);
                base.SubItemRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("增加成功");
                TxtRyName.Focus();
                DataClear();
            }
        }

        private void DataEdit()
        {
            MdlZd_TjJl mdlZdTjJl = _bllZdTjJl.GetModel(TxtCode.Text.Trim());
            SubItemRow["Tj1_Code"] = tj1_Code;
            SubItemRow["Tj2_Code"] = TxtCode.Text.Trim();
            SubItemRow["Ry_Code"] = "";
            SubItemRow["Ry_Name"] = TxtRyName.Text.Trim();
            SubItemRow["Tj2_Date"] = DtpTjDate.Value;
            SubItemRow["Tj2_Bm"] = TxtTjBm.Text.Trim();
            SubItemRow["Tj2_Dh"] = TxtDh.Text.Trim();
            SubItemRow["Tj2_Jkzk"] = TxtJkzk.Text.Trim();
            SubItemRow["Tj2_Memo"] = TxtMemo.Text.Trim();
            SubItemRow["Tj2_LrDate"] = DateTime.Now;
            SubItemRow["Tj2_Jsr_Code"] = YdVar.Var.JsrCode;
            SubItemRow["Tj2_Jsr_Name"] = YdVar.Var.UserName;

            //数据保存
            try
            {
                Common.DataTableToList.ToModel(SubItemRow, mdlZdTjJl);

                _bllZdTjJl.Update(mdlZdTjJl);
                base.SubItemRow.AcceptChanges();

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                TxtRyName.Focus();
            }
        }
        #endregion

        #region 控件动作
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (!DataCheck()) return;
            if (Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }



        #endregion


    }
}
