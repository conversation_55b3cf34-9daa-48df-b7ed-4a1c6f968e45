using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BLL;
using Common.Delegate;
using Model;
using YdPublicFunction;
using Common;

namespace YdGSP
{
    public partial class Zd_PxJh2 : Common.BaseForm.DoubleFormRK1
    {
        private DataTable ZbTable;
        private DataRow ZbRow;
        public Common.Delegate.TransmitTxt ZbTransmitTxt = new TransmitTxt();
        private bool _frmInit = false;
        private BLL.BllZd_PxJh _bllZdPxJh = new BllZd_PxJh();
        private BLL.BllZd_PxJl _bllZdPxJl = new BllZd_PxJl();
        private Model.MdlZd_PxJh _mdlZdPxJh = new MdlZd_PxJh();

        public Zd_PxJh2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            ZbRow = row;
            base.Insert = insert;
            ZbTable = table;
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Zd_PxJh2_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert)
                Zb_Clear();
            else
                Zb_Show();
            BtnState();
            _frmInit = true;
            Thread thread;
            thread = new Thread(this.Cb_Show);
            thread.IsBackground = true;
            thread.Start();
            MessageBox.Show(TxtZt.Height.ToString());
        }
        private void Zd_PxJh2_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        #region 自定义函数

        #region 初始化函数
        private void FormInit()
        {
            base.BaseBtnDelete = BtnDelete;
            base.BaseBtnNew = BtnNew;
            base.BaseBtnSave = BtnSave;
            base.BaseBtnClose = BtnClose;
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;

            BtnDelete.Location = new Point(30, 1);
            BtnDeleteAll.Location = new Point(BtnDelete.Left + BtnDelete.Width + 2, 1);
            BtnNew.Location = new Point(BtnDeleteAll.Left + BtnDeleteAll.Width + 2, 1);
            BtnSave.Location = new Point(BtnNew.Left + BtnNew.Width + 2, 1);
            BtnClose.Location = new Point(BtnSave.Left + BtnSave.Width + 2, 1);

            TxtCode.Enabled = false;

            DtpDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.EditFormat = "yyyy-MM-dd HH:mm:ss";

            myGrid1.Init_Grid();
            myGrid1.Init_Column("培训记录编码", "Px2_Code", 120, "中", "", false);
            myGrid1.Init_Column("人员编码", "Ry_Code", 100, "中", "", false);
            myGrid1.Init_Column("人员姓名", "Ry_Name", 120, "左", "", false);
            myGrid1.Init_Column("培训时间", "Px2_Date", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("考核结果", "Px2_KhJg", 100, "中", "", false);
            myGrid1.Init_Column("是否补训", "Px2_Bx", 80, "中", "", false);
            myGrid1.Init_Column("成绩", "Px2_Cj", 80, "中", "", false);
            myGrid1.Init_Column("评定人", "Px2_Pdr", 100, "左", "", false);
            myGrid1.Init_Column("备注", "Px2_Memo", 200, "左", "", false);
            myGrid1.Init_Column("录入时间", "Lr_Date", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("经手人", "Jsr_Name", 100, "左", "", false);
            myGrid1.AllowSort = true;
            myGrid1.AllowAddNew = true;
        }
        private void BtnState()
        {
            BtnDelete.Enabled = true;
            BtnDeleteAll.Enabled = true;
            BtnNew.Enabled = true;
            BtnSave.Enabled = true;
            BtnClose.Enabled = true;
            ControlEnable(true);
        }
        private void ControlEnable(bool flag)
        {
            TxtZt.Enabled = flag;
            TxtMd.Enabled = flag;
            DtpDate.Enabled = flag;
            TxtDd.Enabled = flag;
            TxtJs.Enabled = flag;
            TxtKs.Enabled = flag;
            TxtDx.Enabled = flag;
            TxtFs.Enabled = flag;
            TxtKhfs.Enabled = flag;
            TxtNr.Enabled = flag;
            TxtMemo.Enabled = flag;
            myGrid1.AllowAddNew = flag;
        }
        #endregion

        #region  显示函数
        protected override void Zb_Clear()
        {
            base.Insert = true;
            _mdlZdPxJh = new MdlZd_PxJh();
            ZbRow = ZbTable.NewRow();
            TxtCode.Text = _bllZdPxJh.MaxCode(10);
            TxtZt.Text = "";
            TxtMd.Text = "";
            DtpDate.Value = DateTime.Now;
            TxtDd.Text = "";
            TxtJs.Text = "";
            TxtKs.Text = "";
            TxtDx.Text = "";
            TxtFs.Text = "";
            TxtKhfs.Text = "";
            TxtNr.Text = "";
            TxtMemo.Text = "";
            TxtZt.Select();
        }

        private void Zb_Show()
        {
            _mdlZdPxJh = _bllZdPxJh.GetModel(ZbRow["Px1_Code"] + "");
            TxtCode.Text = _mdlZdPxJh.Px1_Code;
            TxtZt.Text = _mdlZdPxJh.Px1_Zt;
            TxtMd.Text = _mdlZdPxJh.Px1_Md;
            DtpDate.Value = _mdlZdPxJh.Px1_Date ?? DateTime.Now;
            TxtDd.Text = _mdlZdPxJh.Px1_Dd;
            TxtJs.Text = _mdlZdPxJh.Px1_Js;
            TxtKs.Text = _mdlZdPxJh.Px1_Ks;
            TxtDx.Text = _mdlZdPxJh.Px1_Dx;
            TxtFs.Text = _mdlZdPxJh.Px1_Fs;
            TxtKhfs.Text = _mdlZdPxJh.Px1_Khfs;
            TxtNr.Text = _mdlZdPxJh.Px1_Nr;
            TxtMemo.Text = _mdlZdPxJh.Px1_Memo;
            TxtZt.Select();
        }
        private void Cb_Show()
        {
            MyTable = _bllZdPxJl.GetList($"Px1_Code='{_mdlZdPxJh.Px1_Code}'").Tables[0];
            MyTable.TableName = "明细";
            //主表记录
            MyCm = (CurrencyManager)BindingContext[MyTable];
            myGrid1.BeginInvoke(new Action<DataTable>(p =>
            {
                myGrid1.DataTable = p;
                this.LblTotal.Text = "∑=" + p.Rows.Count;
            }), MyTable);
            DataSum("");
        }
        #endregion

        #region 检查语句
        //检查主表数据
        private bool ZbCheck()
        {
            if (!this.Insert && _bllZdPxJh.GetRecordCount("Px1_Code='" + TxtCode.Text + "'") == 0)
            {
                MessageBox.Show("此培训计划已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (TxtZt.Text.Trim() == "")
            {
                MessageBox.Show("培训主题不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtZt.Focus();
                return false;
            }
            if (CustomControl.Func.NotAllowEmpty(DtpDate)) return false;
            if ((DateTime)DtpDate.Value > Convert.ToDateTime("2079-06-01"))
            {
                DtpDate.Select();
                MessageBox.Show("填写的培训时间超出范围，请重新输入!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            return true;
        }
        private bool ZbStateCheck(string state)
        {
            if (state == "从表修改")
            {
                // 从表修改时的检查逻辑
            }
            if (state == "删除")
            {
                if (!this.Insert && _bllZdPxJh.GetRecordCount("Px1_Code='" + TxtCode.Text + "'") == 0)
                {
                    MessageBox.Show("此培训计划已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 按钮函数
        //删除行
        protected override bool DataDeleteOne()
        {
            if (myGrid1.Row >= myGrid1.RowCount)
            {
                MessageBox.Show("请选择一条记录!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            if (base.DataDeleteOne() == true)
            {
                _bllZdPxJl.Delete(base.SubItemRow["Px2_Code"] + "");
                MyTable.AcceptChanges();
                DataSum("");
                LblTotal.Text = "∑=" + (MyTable.Rows.Count).ToString();
                return true;
            }
            return false;
        }

        protected override bool DataDeleteAll(string PrimaryKey, Func<string, bool> Delete)
        {

            if (_mdlZdPxJh == null)
            {
                MessageBox.Show("数据尚未保存,无法删除!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            ZbRow.Delete();
            return base.DataDeleteAll(PrimaryKey, Delete);
        }

        protected override void DataNew()
        {
            myGrid1.UpdateData();
            if (base.MyTable.DataSet.HasChanges() == true)
            {
                if (MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.OK)
                {
                    DataSave(true);
                }
            }

            Zb_Clear();
            BtnState();
            Cb_Show();
            TxtZt.Select();
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="showMsgbox">是否弹出提示框</param>
        /// <returns></returns>
        protected override bool DataSave(bool showMsgbox)
        {
            if (ZbCheck() == false) return false;
            if (base.Insert == true) TxtCode.Text = _bllZdPxJh.MaxCode(10);
            DataSum("");
            if (base.Insert == true)
            {
                //增加记录
                Zb_Add();
            }
            else
            {
                //编辑记录
                Zb_Edit();
            }
            if (showMsgbox == true) MessageBox.Show("数据保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return true;
        }

        #region 数据操作函数

        protected override void DataSum(string s)
        {
            if (base.BaseLblTotal != null)
            {
                this.BeginInvoke(new Action(() =>
                {
                    base.BaseLblTotal.Text = "∑=" + base.MyTable.Rows.Count.ToString();
                }));
            }
        }

        #region 主表

        //增加记录
        private void Zb_Add()
        {
            _mdlZdPxJh.Px1_Code = _bllZdPxJh.MaxCode(10);
            TxtCode.Text = _mdlZdPxJh.Px1_Code;
            _mdlZdPxJh.Px1_Zt = TxtZt.Text.Trim();
            _mdlZdPxJh.Px1_Md = TxtMd.Text.Trim();
            _mdlZdPxJh.Px1_Date = (DateTime)DtpDate.Value;
            _mdlZdPxJh.Px1_Dd = TxtDd.Text.Trim();
            _mdlZdPxJh.Px1_Js = TxtJs.Text.Trim();
            _mdlZdPxJh.Px1_Ks = TxtKs.Text.Trim();
            _mdlZdPxJh.Px1_Dx = TxtDx.Text.Trim();
            _mdlZdPxJh.Px1_Fs = TxtFs.Text.Trim();
            _mdlZdPxJh.Px1_Khfs = TxtKhfs.Text.Trim();
            _mdlZdPxJh.Px1_Nr = TxtNr.Text.Trim();
            _mdlZdPxJh.Px1_Memo = TxtMemo.Text.Trim();
            _bllZdPxJh.Add(_mdlZdPxJh);
            Common.DataTableToList.ToDataRow<MdlZd_PxJh>(_mdlZdPxJh, ZbRow);
            ZbTable.Rows.Add(ZbRow);
            base.Insert = false;
        }
        //编辑记录
        private void Zb_Edit()
        {
            _mdlZdPxJh.Px1_Zt = TxtZt.Text.Trim();
            _mdlZdPxJh.Px1_Md = TxtMd.Text.Trim();
            _mdlZdPxJh.Px1_Date = (DateTime)DtpDate.Value;
            _mdlZdPxJh.Px1_Dd = TxtDd.Text.Trim();
            _mdlZdPxJh.Px1_Js = TxtJs.Text.Trim();
            _mdlZdPxJh.Px1_Ks = TxtKs.Text.Trim();
            _mdlZdPxJh.Px1_Dx = TxtDx.Text.Trim();
            _mdlZdPxJh.Px1_Fs = TxtFs.Text.Trim();
            _mdlZdPxJh.Px1_Khfs = TxtKhfs.Text.Trim();
            _mdlZdPxJh.Px1_Nr = TxtNr.Text.Trim();
            _mdlZdPxJh.Px1_Memo = TxtMemo.Text.Trim();
            _bllZdPxJh.Update(_mdlZdPxJh);
            Common.DataTableToList.ToDataRow<MdlZd_PxJh>(_mdlZdPxJh, ZbRow);
        }
        #endregion

        #region 从表

        protected override void SubDataEdit()
        {
            if (ZbCheck() == false) return;
            if (ZbStateCheck("从表修改") == false) return;
            DataSave(false);
            bool subInsert;
            if ((myGrid1.Row + 1) > myGrid1.RowCount)
            {
                base.SubItemRow = base.MyTable.NewRow();
                subInsert = true;
            }
            else
            {
                base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
                subInsert = false;
            }

            Zd_PxJh3 f = new Zd_PxJh3(_mdlZdPxJh.Px1_Code, subInsert, base.SubItemRow, base.MyTable);
            // f.MyTransmitDataRow = this.MyTransmitDataRow;
            f.MyTransmitTxt = this.MyTransmitTxt;
            f.Owner = this;
            f.ShowDialog();

        }

        #endregion

        #endregion

        #endregion

        #endregion

        #region 控件动作

        #region 按钮动作

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DataDeleteOne();
        }
        private void BtnDeleteAll_Click(object sender, EventArgs e)
        {
            DataDeleteAll(_mdlZdPxJh.Px1_Code, _bllZdPxJh.Delete);
        }
        private void BtnNew_Click(object sender, EventArgs e)
        {
            DataNew();
        }
        private void BtnSave_Click(object sender, EventArgs e)
        {
            DataSave(true);
        }
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Grid动作
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    this.SubDataEdit();
                    break;
            }
        }
        #endregion

        #endregion


    }
}
