﻿/**  版本信息模板在安装目录下，可自行修改。
* DalZd_TjJl.cs
*
* 功 能： N/A
* 类 名： DalZd_TjJl
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2025-07-24 11:07:21   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
using System.Data;
using System.Text;
using System.Data.SqlClient;
using IDAL;
using DBUtility;//Please add references
namespace SQLServerDAL
{
	/// <summary>
	/// 数据访问类:DalZd_TjJl
	/// </summary>
	public partial class DalZd_TjJl : IDalZd_TjJl
	{
		public DalZd_TjJl()
		{ }
		#region  BasicMethod

		/// <summary>
		/// 是否存在该记录
		/// </summary>
		public bool Exists(string Tj2_Code)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) from Zd_TjJl");
			strSql.Append(" where Tj2_Code=@Tj2_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj2_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Tj2_Code;

			return Common.WinFormVar.Var.DbHelper.Exists(strSql.ToString(), parameters);
		}


		/// <summary>
		/// 增加一条数据
		/// </summary>
		public bool Add(Model.MdlZd_TjJl model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("insert into Zd_TjJl(");
			strSql.Append("Tj2_Code,Tj1_Code,Ry_Code,Ry_Name,Tj2_Date,Tj2_Bm,Tj2_Dh,Tj2_Jkzk,Tj2_Memo,Tj2_LrDate,Tj2_Jsr_Code,Tj2_Jsr_Name,Fj_Sl)");
			strSql.Append(" values (");
			strSql.Append("@Tj2_Code,@Tj1_Code,@Ry_Code,@Ry_Name,@Tj2_Date,@Tj2_Bm,@Tj2_Dh,@Tj2_Jkzk,@Tj2_Memo,@Tj2_LrDate,@Tj2_Jsr_Code,@Tj2_Jsr_Name,@Fj_Sl)");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj2_Code", SqlDbType.Char,10),
					new SqlParameter("@Tj1_Code", SqlDbType.Char,10),
					new SqlParameter("@Ry_Code", SqlDbType.Char,7),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Tj2_Bm", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Dh", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Jkzk", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Memo", SqlDbType.VarChar,500),
					new SqlParameter("@Tj2_LrDate", SqlDbType.DateTime),
					new SqlParameter("@Tj2_Jsr_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Jsr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Fj_Sl", SqlDbType.Int,4)};
			parameters[0].Value = model.Tj2_Code;
			parameters[1].Value = model.Tj1_Code;
			parameters[2].Value = model.Ry_Code;
			parameters[3].Value = model.Ry_Name;
			parameters[4].Value = model.Tj2_Date;
			parameters[5].Value = model.Tj2_Bm;
			parameters[6].Value = model.Tj2_Dh;
			parameters[7].Value = model.Tj2_Jkzk;
			parameters[8].Value = model.Tj2_Memo;
			parameters[9].Value = model.Tj2_LrDate;
			parameters[10].Value = model.Tj2_Jsr_Code;
			parameters[11].Value = model.Tj2_Jsr_Name;
			parameters[12].Value = model.Fj_Sl;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 更新一条数据
		/// </summary>
		public bool Update(Model.MdlZd_TjJl model)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("update Zd_TjJl set ");
			strSql.Append("Tj1_Code=@Tj1_Code,");
			strSql.Append("Ry_Code=@Ry_Code,");
			strSql.Append("Ry_Name=@Ry_Name,");
			strSql.Append("Tj2_Date=@Tj2_Date,");
			strSql.Append("Tj2_Bm=@Tj2_Bm,");
			strSql.Append("Tj2_Dh=@Tj2_Dh,");
			strSql.Append("Tj2_Jkzk=@Tj2_Jkzk,");
			strSql.Append("Tj2_Memo=@Tj2_Memo,");
			strSql.Append("Tj2_LrDate=@Tj2_LrDate,");
			strSql.Append("Tj2_Jsr_Code=@Tj2_Jsr_Code,");
			strSql.Append("Tj2_Jsr_Name=@Tj2_Jsr_Name,");
			strSql.Append("Fj_Sl=@Fj_Sl");
			strSql.Append(" where Tj2_Code=@Tj2_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj1_Code", SqlDbType.Char,10),
					new SqlParameter("@Ry_Code", SqlDbType.Char,7),
					new SqlParameter("@Ry_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Date", SqlDbType.SmallDateTime),
					new SqlParameter("@Tj2_Bm", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Dh", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Jkzk", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Memo", SqlDbType.VarChar,500),
					new SqlParameter("@Tj2_LrDate", SqlDbType.DateTime),
					new SqlParameter("@Tj2_Jsr_Code", SqlDbType.VarChar,50),
					new SqlParameter("@Tj2_Jsr_Name", SqlDbType.VarChar,50),
					new SqlParameter("@Fj_Sl", SqlDbType.Int,4),
					new SqlParameter("@Tj2_Code", SqlDbType.Char,10)};
			parameters[0].Value = model.Tj1_Code;
			parameters[1].Value = model.Ry_Code;
			parameters[2].Value = model.Ry_Name;
			parameters[3].Value = model.Tj2_Date;
			parameters[4].Value = model.Tj2_Bm;
			parameters[5].Value = model.Tj2_Dh;
			parameters[6].Value = model.Tj2_Jkzk;
			parameters[7].Value = model.Tj2_Memo;
			parameters[8].Value = model.Tj2_LrDate;
			parameters[9].Value = model.Tj2_Jsr_Code;
			parameters[10].Value = model.Tj2_Jsr_Name;
			parameters[11].Value = model.Fj_Sl;
			parameters[12].Value = model.Tj2_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		/// 删除一条数据
		/// </summary>
		public bool Delete(string Tj2_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_TjJl ");
			strSql.Append(" where Tj2_Code=@Tj2_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj2_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Tj2_Code;

			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString(), parameters);
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		/// <summary>
		/// 批量删除数据
		/// </summary>
		public bool DeleteList(string Tj2_Codelist)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("delete from Zd_TjJl ");
			strSql.Append(" where Tj2_Code in (" + Tj2_Codelist + ")  ");
			int rows = Common.WinFormVar.Var.DbHelper.ExecuteSql(strSql.ToString());
			if (rows > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_TjJl GetModel(string Tj2_Code)
		{

			StringBuilder strSql = new StringBuilder();
			strSql.Append("select  top 1 Tj2_Code,Tj1_Code,Ry_Code,Ry_Name,Tj2_Date,Tj2_Bm,Tj2_Dh,Tj2_Jkzk,Tj2_Memo,Tj2_LrDate,Tj2_Jsr_Code,Tj2_Jsr_Name,Fj_Sl from Zd_TjJl ");
			strSql.Append(" where Tj2_Code=@Tj2_Code ");
			SqlParameter[] parameters = {
					new SqlParameter("@Tj2_Code", SqlDbType.Char,10)            };
			parameters[0].Value = Tj2_Code;

			Model.MdlZd_TjJl model = new Model.MdlZd_TjJl();
			DataSet ds = Common.WinFormVar.Var.DbHelper.Query(strSql.ToString(), parameters);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return DataRowToModel(ds.Tables[0].Rows[0]);
			}
			else
			{
				return null;
			}
		}


		/// <summary>
		/// 得到一个对象实体
		/// </summary>
		public Model.MdlZd_TjJl DataRowToModel(DataRow row)
		{
			Model.MdlZd_TjJl model = new Model.MdlZd_TjJl();
			if (row != null)
			{
				if (row["Tj2_Code"] != null)
				{
					model.Tj2_Code = row["Tj2_Code"].ToString();
				}
				if (row["Tj1_Code"] != null)
				{
					model.Tj1_Code = row["Tj1_Code"].ToString();
				}
				if (row["Ry_Code"] != null)
				{
					model.Ry_Code = row["Ry_Code"].ToString();
				}
				if (row["Ry_Name"] != null)
				{
					model.Ry_Name = row["Ry_Name"].ToString();
				}
				if (row["Tj2_Date"] != null && row["Tj2_Date"].ToString() != "")
				{
					model.Tj2_Date = DateTime.Parse(row["Tj2_Date"].ToString());
				}
				if (row["Tj2_Bm"] != null)
				{
					model.Tj2_Bm = row["Tj2_Bm"].ToString();
				}
				if (row["Tj2_Dh"] != null)
				{
					model.Tj2_Dh = row["Tj2_Dh"].ToString();
				}
				if (row["Tj2_Jkzk"] != null)
				{
					model.Tj2_Jkzk = row["Tj2_Jkzk"].ToString();
				}
				if (row["Tj2_Memo"] != null)
				{
					model.Tj2_Memo = row["Tj2_Memo"].ToString();
				}
				if (row["Tj2_LrDate"] != null && row["Tj2_LrDate"].ToString() != "")
				{
					model.Tj2_LrDate = DateTime.Parse(row["Tj2_LrDate"].ToString());
				}
				if (row["Tj2_Jsr_Code"] != null)
				{
					model.Tj2_Jsr_Code = row["Tj2_Jsr_Code"].ToString();
				}
				if (row["Tj2_Jsr_Name"] != null)
				{
					model.Tj2_Jsr_Name = row["Tj2_Jsr_Name"].ToString();
				}
				if (row["Fj_Sl"] != null && row["Fj_Sl"].ToString() != "")
				{
					model.Fj_Sl = int.Parse(row["Fj_Sl"].ToString());
				}
			}
			return model;
		}

		/// <summary>
		/// 获得数据列表
		/// </summary>
		public DataSet GetList(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select Tj2_Code,Tj1_Code,Ry_Code,Ry_Name,Tj2_Date,Tj2_Bm,Tj2_Dh,Tj2_Jkzk,Tj2_Memo,Tj2_LrDate,Tj2_Jsr_Code,Tj2_Jsr_Name,Fj_Sl ");
			strSql.Append(" FROM Zd_TjJl ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获得前几行数据
		/// </summary>
		public DataSet GetList(int Top, string strWhere, string filedOrder)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select ");
			if (Top > 0)
			{
				strSql.Append(" top " + Top.ToString());
			}
			strSql.Append(" Tj2_Code,Tj1_Code,Ry_Code,Ry_Name,Tj2_Date,Tj2_Bm,Tj2_Dh,Tj2_Jkzk,Tj2_Memo,Tj2_LrDate,Tj2_Jsr_Code,Tj2_Jsr_Name,Fj_Sl ");
			strSql.Append(" FROM Zd_TjJl ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			strSql.Append(" order by " + filedOrder);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/// <summary>
		/// 获取记录总数
		/// </summary>
		public int GetRecordCount(string strWhere)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("select count(1) FROM Zd_TjJl ");
			if (strWhere.Trim() != "")
			{
				strSql.Append(" where " + strWhere);
			}
			object obj = Common.WinFormVar.Var.DbHelper.GetSingle(strSql.ToString());
			if (obj == null)
			{
				return 0;
			}
			else
			{
				return Convert.ToInt32(obj);
			}
		}
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetListByPage(string strWhere, string orderby, int startIndex, int endIndex)
		{
			StringBuilder strSql = new StringBuilder();
			strSql.Append("SELECT * FROM ( ");
			strSql.Append(" SELECT ROW_NUMBER() OVER (");
			if (!string.IsNullOrEmpty(orderby.Trim()))
			{
				strSql.Append("order by T." + orderby);
			}
			else
			{
				strSql.Append("order by T.Tj2_Code desc");
			}
			strSql.Append(")AS Row, T.*  from Zd_TjJl T ");
			if (!string.IsNullOrEmpty(strWhere.Trim()))
			{
				strSql.Append(" WHERE " + strWhere);
			}
			strSql.Append(" ) TT");
			strSql.AppendFormat(" WHERE TT.Row between {0} and {1}", startIndex, endIndex);
			return Common.WinFormVar.Var.DbHelper.Query(strSql.ToString());
		}

		/*
		/// <summary>
		/// 分页获取数据列表
		/// </summary>
		public DataSet GetList(int PageSize,int PageIndex,string strWhere)
		{
			SqlParameter[] parameters = {
					new SqlParameter("@tblName", SqlDbType.VarChar, 255),
					new SqlParameter("@fldName", SqlDbType.VarChar, 255),
					new SqlParameter("@PageSize", SqlDbType.Int),
					new SqlParameter("@PageIndex", SqlDbType.Int),
					new SqlParameter("@IsReCount", SqlDbType.Bit),
					new SqlParameter("@OrderType", SqlDbType.Bit),
					new SqlParameter("@strWhere", SqlDbType.VarChar,1000),
					};
			parameters[0].Value = "Zd_TjJl";
			parameters[1].Value = "Tj2_Code";
			parameters[2].Value = PageSize;
			parameters[3].Value = PageIndex;
			parameters[4].Value = 0;
			parameters[5].Value = 0;
			parameters[6].Value = strWhere;	
			return Common.WinFormVar.Var.DbHelper.RunProcedure("UP_GetRecordByPage",parameters,"ds");
		}*/

		#endregion  BasicMethod
		#region  ExtensionMethod

		/// <summary>
		/// 得到最大编码
		/// </summary>
		public string MaxCode(int length)
		{
			string max = DateTime.Now.ToString("yyMMdd") + Common.WinFormVar.Var.DbHelper.F_MaxCode($"SELECT MAX(RIGHT(Tj2_Code,4)) FROM Zd_TjJl WHERE LEFT(Tj2_Code,6)='" + DateTime.Now.ToString("yyMMdd") + "'", length - 6);
			return max;
		}

		#endregion  ExtensionMethod
	}
}

