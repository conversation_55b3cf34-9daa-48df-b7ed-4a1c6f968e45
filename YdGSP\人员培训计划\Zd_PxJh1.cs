using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using BLL;
using Model;
using YdPublicFunction;

namespace YdGSP
{
    public partial class Zd_PxJh1 : Common.BaseForm.BaseDict1
    {
        private BLL.BllZd_PxJh _bllZdPxJh = new BllZd_PxJh();
        public Zd_PxJh1()
        {
            InitializeComponent();
        }

        private void Zd_PxJh1_Load(object sender, EventArgs e)
        {
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;
            FormInit();
            DataInit();
            myGrid1.Select();
        }
        #region 初始化
        private void FormInit()
        {
            doubleDateEdit1.CustomFormat = "yyyy-MM-dd";
            doubleDateEdit1.DisplayFormat = "yyyy-MM-dd";
            doubleDateEdit1.EditFormat = "yyyy-MM-dd";
            doubleDateEdit1.SelectedIndex = 5;

            myGrid1.Init_Grid();
            myGrid1.Init_Column("培训编码", "Px1_Code", 120, "中", "", false);
            myGrid1.Init_Column("培训主题", "Px1_Zt", 280, "左", "", false);
            myGrid1.Init_Column("培训目的", "Px1_Md", 200, "左", "", false);
            myGrid1.Init_Column("培训时间", "Px1_Date", 180, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("培训地点", "Px1_Dd", 120, "左", "", false);
            myGrid1.Init_Column("培训讲师", "Px1_Js", 120, "左", "", false);
            myGrid1.Init_Column("课时", "Px1_Ks", 80, "中", "", false);
            myGrid1.Init_Column("培训对象", "Px1_Dx", 120, "左", "", false);
            myGrid1.Init_Column("培训方式", "Px1_Fs", 120, "左", "", false);
            myGrid1.Init_Column("考核方式", "Px1_Khfs", 120, "左", "", false);
            myGrid1.Init_Column("备注", "Px1_Memo", 200, "左", "", false);
            myGrid1.ColumnFooters = true;
            myGrid1.AllowSort = true;
        }
        #endregion

        #region 自定义函数
        private void DataInit()
        {
            base.MyTable = _bllZdPxJh.GetList($"Px1_Date between '{DateTime.Parse(doubleDateEdit1.StartValue.ToString()).ToString("yyy-MM-dd")}' " +
                                             $"And '{DateTime.Parse(doubleDateEdit1.EndValue.ToString()).ToString("yyy-MM-dd 23:59:59")}'").Tables[0];
            base.MyTable.PrimaryKey = new DataColumn[] { base.MyTable.Columns["Px1_Code"] };
            base.MyCm = (CurrencyManager)BindingContext[base.MyTable, ""];
            myGrid1.BeginInvoke(new Action(() => this.myGrid1.DataTable = base.MyTable));
            base.MyView = (DataView)base.MyCm.List;
            base.MyView.Sort = "Px1_Date desc";
            DataSum();
        }
        protected override void DataEdit(bool insert)
        {
            base.Insert = insert;
            if (base.Insert == true)
            {
                base.MyRow = base.MyTable.NewRow();
            }
            else
            {
                if (this.myGrid1.RowCount == 0)
                {
                    return;
                }
                base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            }

            Zd_PxJh2 vform = new Zd_PxJh2(base.Insert, base.MyRow, base.MyTable);
            vform.Tag = base.MyRow["Px1_Code"];
            vform.ZbTransmitTxt = base.MyTransmitTxt;
            base.AddTabControl(vform, "人员培训计划明细-" + (base.MyRow["Px1_Zt"].ToString() == "" ? "新计划" : base.MyRow["Px1_Zt"].ToString()), YdResources.C_Resources.GetImage16(""));
        }
        protected override void DataDelete()
        {
            if (myGrid1.Row + 1 > myGrid1.RowCount)
            {
                MessageBox.Show("请选择培训计划记录!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            base.MyRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;

            if (MessageBox.Show("是否删除:人员培训计划【" + base.MyRow["Px1_Zt"] + "】记录?", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No) return;

            if (_bllZdPxJh.Delete(base.MyRow["Px1_Code"].ToString()) == true)
            {
                myGrid1.Delete();
                base.MyTable.AcceptChanges();
                DataSum();
                MessageBox.Show("数据删除成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
        }

        private void DataSum()
        {
            LblTotal.BeginInvoke(new Action(() => this.LblTotal.Text = "∑=" + this.myGrid1.Splits[0].Rows.Count.ToString()));
        }
        #endregion

        #region 事件
        private void Cmd_Add_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataEdit(true);
        }
        private void Cmd_Del_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataDelete();
        }
        private void CmdQuery_Click(object sender, C1.Win.C1Command.ClickEventArgs e)
        {
            DataInit();
        }
        private void TxtFilter_TextChanged(object sender, EventArgs e)
        {
            char[] split = { ' ' };
            string filter = "Px1_Zt+Px1_Js+Px1_Dx";
            string strFilter = "";
            foreach (string substr in TxtFilter.Text.Replace("*", "[*]").Replace("%", "[%]").Split(split))
            {
                strFilter = strFilter + filter + " like '*" + substr + "*' And ";
            }
            strFilter = strFilter.Substring(0, strFilter.Length - 5);
            MyView.RowFilter = strFilter;
            DataSum();
        }
        #endregion
    }
}
