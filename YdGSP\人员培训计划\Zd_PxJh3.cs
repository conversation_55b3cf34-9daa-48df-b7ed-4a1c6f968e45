using BLL;
using Model;
using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace YdGSP
{
    public partial class Zd_PxJh3 : Common.BaseForm.DoubleFormRK2
    {
        private string px1_Code;
        private bool _frmInit = false;
        private BllZd_PxJl _bllZdPxJl = new BllZd_PxJl();
        public Zd_PxJh3(string px1_Code, bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            this.px1_Code = px1_Code;
            base.Insert = insert;
            base.SubItemRow = row;
            base.MyTable = table;
            TxtRyName.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Zd_PxJh3_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert == true)
                this.DataClear();
            else
                this.DataShow(base.SubItemRow);
            _frmInit = true;
        }

        #region 初始化函数
        private void FormInit()
        {
            TxtCode.Enabled = false;

            DtpPxDate.EditFormat = "yyyy-MM-dd HH:mm:ss";
            DtpPxDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DtpPxDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";

            TxtKhJg.GotFocus += new System.EventHandler(base.InputCn);
            TxtBx.GotFocus += new System.EventHandler(base.InputCn);
            TxtCj.GotFocus += new System.EventHandler(base.InputEn);
            TxtPdr.GotFocus += new System.EventHandler(base.InputCn);
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);

            panel1.Height = 38;
            BtnSave.Location = new Point(Width - BtnSave.Width * 2 - 20 - 3, 1);
            BtnCancel.Location = new Point(BtnSave.Right + 3, 1);
        }
        #endregion

        #region 自定义函数
        private void DataClear()
        {
            base.Insert = true;
            TxtCode.Text = _bllZdPxJl.MaxCode(10);
            TxtRyName.Text = "";
            DtpPxDate.Value = DateTime.Now;
            TxtKhJg.Text = "";
            TxtBx.Text = "";
            TxtCj.Text = "";
            TxtPdr.Text = "";
            TxtMemo.Text = "";

            TxtRyName.Select();
        }
        protected override void DataShow(DataRow row)
        {
            base.Insert = false;

            TxtCode.Text = row["Px2_Code"] + "";
            TxtRyName.Text = row["Ry_Name"] + "";

            if (row["Px2_Date"] != DBNull.Value)
                DtpPxDate.Value = Convert.ToDateTime(row["Px2_Date"]);
            else
                DtpPxDate.Value = DateTime.Now;

            TxtKhJg.Text = row["Px2_KhJg"] + "";
            TxtBx.Text = row["Px2_Bx"] + "";
            TxtCj.Text = row["Px2_Cj"] + "";
            TxtPdr.Text = row["Px2_Pdr"] + "";
            TxtMemo.Text = row["Px2_Memo"] + "";

            TxtRyName.Select();
        }

        private bool DataCheck()
        {
            if (TxtRyName.Text.Trim() == "")
            {
                MessageBox.Show("人员姓名不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtRyName.Focus();
                return false;
            }
            if (Insert)
            {
                if (MyTable.AsEnumerable().Where(p => p["Ry_Name"].ToString() == TxtRyName.Text.Trim()).Count() > 0)
                {
                    MessageBox.Show($"请勿重复添加此人员 {TxtRyName.Text}!", "提示", MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    TxtRyName.Select();
                    return false;
                }
            }
            else
            {
                if (MyTable.AsEnumerable().Where(p => p["Ry_Name"].ToString() == TxtRyName.Text.Trim() && p["Px2_Code"].ToString() != TxtCode.Text.Trim()).Count() > 0)
                {
                    MessageBox.Show($"请勿重复添加此人员 {TxtRyName.Text}!", "提示", MessageBoxButtons.OK,
                        MessageBoxIcon.Warning);
                    TxtRyName.Select();
                    return false;
                }
            }

            return true;
        }

        private void DataAdd()
        {
            base.SubItemRow = base.MyTable.NewRow();

            SubItemRow["Px1_Code"] = px1_Code;
            SubItemRow["Px2_Code"] = TxtCode.Text.Trim();
            SubItemRow["Ry_Code"] = "";
            SubItemRow["Ry_Name"] = TxtRyName.Text.Trim();
            SubItemRow["Px2_Date"] = DtpPxDate.Value;
            SubItemRow["Px2_KhJg"] = TxtKhJg.Text.Trim();
            SubItemRow["Px2_Bx"] = TxtBx.Text.Trim();
            SubItemRow["Px2_Cj"] = TxtCj.Text.Trim();
            SubItemRow["Px2_Pdr"] = TxtPdr.Text.Trim();
            SubItemRow["Px2_Memo"] = TxtMemo.Text.Trim();
            SubItemRow["Lr_Date"] = DateTime.Now;
            SubItemRow["Jsr_Code"] = YdVar.Var.JsrCode;
            SubItemRow["Jsr_Name"] = YdVar.Var.UserName;

            //数据保存
            try
            {
                MdlZd_PxJl mdlZdPxJl = Common.DataTableToList.ToModel<MdlZd_PxJl>(SubItemRow);

                _bllZdPxJl.Add(mdlZdPxJl);
                base.MyTable.Rows.Add(base.SubItemRow);
                base.SubItemRow.AcceptChanges();
                base.MyTransmitTxt.OnSetText("最后");
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("增加成功");
                TxtRyName.Focus();
                DataClear();
            }
        }

        private void DataEdit()
        {
            MdlZd_PxJl mdlZdPxJl = _bllZdPxJl.GetModel(TxtCode.Text.Trim());
            SubItemRow["Px1_Code"] = px1_Code;
            SubItemRow["Px2_Code"] = TxtCode.Text.Trim();
            SubItemRow["Ry_Code"] = "";
            SubItemRow["Ry_Name"] = TxtRyName.Text.Trim();
            SubItemRow["Px2_Date"] = DtpPxDate.Value;
            SubItemRow["Px2_KhJg"] = TxtKhJg.Text.Trim();
            SubItemRow["Px2_Bx"] = TxtBx.Text.Trim();
            SubItemRow["Px2_Cj"] = TxtCj.Text.Trim();
            SubItemRow["Px2_Pdr"] = TxtPdr.Text.Trim();
            SubItemRow["Px2_Memo"] = TxtMemo.Text.Trim();
            SubItemRow["Lr_Date"] = DateTime.Now;
            SubItemRow["Jsr_Code"] = YdVar.Var.JsrCode;
            SubItemRow["Jsr_Name"] = YdVar.Var.UserName;

            //数据保存
            try
            {
                Common.DataTableToList.ToModel(SubItemRow, mdlZdPxJl);

                _bllZdPxJl.Update(mdlZdPxJl);
                base.SubItemRow.AcceptChanges();

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }
            finally
            {
                CustomSunnyUI.MyShowNotifier.ShowSuccessNotifier("修改成功");
                TxtRyName.Focus();
            }
        }
        #endregion

        #region 控件动作
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (!DataCheck()) return;
            if (Insert == true)
            {
                DataAdd();
            }
            else
            {
                DataEdit();
            }
        }



        #endregion


    }
}
