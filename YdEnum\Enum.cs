﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YdEnum
{

    /// <summary>
    /// 性别编码
    /// </summary>
    public enum Sex_Code
    {
        男,
        女
    }

    /// <summary>
    /// 操作员类别
    /// </summary>
    public enum CzyLb_Code
    {
        采购人员,
        验收人员,
        销售人员,
        审核药师
    }

    /// <summary>
    /// 设备状态
    /// </summary>
    public enum Sb_Zt
    {
        运行,
        维护
    }

    /// <summary>
    /// 企业性质
    /// </summary>
    public enum QyXz_Code
    {
        生产企业,
        经营企业
    }

    /// <summary>
    /// 体检季度
    /// </summary>
    public enum TjJd_Code
    {
        第一季度,
        第二季度,
        第三季度,
        第四季度
    }

    /// <summary>
    /// 不良反应结果
    /// </summary>
    public enum BlfyJg
    {
        治愈,
        好转,
        有后遗症,
        未治愈
    }
    /// <summary>
    /// 不良反应
    /// </summary>
    public enum Blfy
    {
        不详,
        有,
        无
    }
    /// <summary>
    /// 对原患疾病的影响
    /// </summary>
    public enum BlfyYx
    {
        不明显,
        病程延长,
        病情加重,
        导致后遗症
    }
    /// <summary>
    /// 是否需要继续用药
    /// </summary>
    public enum YesNo
    {
        是,
        否
    }
    /// <summary>
    /// 评价
    /// </summary>
    public enum Pj
    {
        未评价,
        肯定,
        可能,
        很可能,
        不太可能
    }
}
