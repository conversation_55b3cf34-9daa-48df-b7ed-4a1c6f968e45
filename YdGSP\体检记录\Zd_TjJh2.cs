using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using BLL;
using Common.Delegate;
using Model;
using YdPublicFunction;
using Common;

namespace YdGSP
{
    public partial class Zd_TjJh2 : Common.BaseForm.DoubleFormRK1
    {
        private DataTable ZbTable;
        private DataRow ZbRow;
        public Common.Delegate.TransmitTxt ZbTransmitTxt = new TransmitTxt();
        private bool _frmInit = false;
        private BLL.BllZd_TjJh _bllZdTjJh = new BllZd_TjJh();
        private BLL.BllZd_TjJl _bllZdTjJl = new BllZd_TjJl();
        private Model.MdlZd_TjJh _mdlZdTjJh = new MdlZd_TjJh();

        public Zd_TjJh2(bool insert, DataRow row, DataTable table)
        {
            InitializeComponent();
            ZbRow = row;
            base.Insert = insert;
            ZbTable = table;
            TxtMemo.GotFocus += new System.EventHandler(base.InputCn);
        }

        private void Zd_TjJh2_Load(object sender, EventArgs e)
        {
            _frmInit = false;
            FormInit();
            if (base.Insert)
                Zb_Clear();
            else
                Zb_Show();
            BtnState();
            _frmInit = true;
            Thread thread;
            thread = new Thread(this.Cb_Show);
            thread.IsBackground = true;
            thread.Start();
        }
        private void Zd_TjJh2_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        #region 自定义函数

        #region 初始化函数
        private void FormInit()
        {
            base.BaseBtnDelete = BtnDelete;
            base.BaseBtnNew = BtnNew;
            base.BaseBtnSave = BtnSave;
            base.BaseBtnClose = BtnClose;
            base.BaseMyGrid = myGrid1;
            base.BaseLblTotal = LblTotal;

            BtnDelete.Location = new Point(30, 1);
            BtnDeleteAll.Location = new Point(BtnDelete.Left + BtnDelete.Width + 2, 1);
            BtnNew.Location = new Point(BtnDeleteAll.Left + BtnDeleteAll.Width + 2, 1);
            BtnSave.Location = new Point(BtnNew.Left + BtnNew.Width + 2, 1);
            BtnClose.Location = new Point(BtnSave.Left + BtnSave.Width + 2, 1);

            TxtCode.Enabled = false;

            DtpDate.CustomFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.DisplayFormat = "yyyy-MM-dd HH:mm:ss";
            DtpDate.EditFormat = "yyyy-MM-dd HH:mm:ss";

            // 初始化体检时间下拉框
            CmbJd.Init();

            myGrid1.Init_Grid();
            myGrid1.Init_Column("体检记录编码", "Tj2_Code", 120, "中", "", false);
            myGrid1.Init_Column("人员姓名", "Ry_Name", 120, "左", "", false);
            myGrid1.Init_Column("体检时间", "Tj2_Date", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("体检部门", "Tj2_Bm", 120, "左", "", false);
            myGrid1.Init_Column("报告单号", "Tj2_Dh", 120, "中", "", false);
            myGrid1.Init_Column("健康状况", "Tj2_Jkzk", 120, "左", "", false);
            myGrid1.Init_Column("体检说明", "Tj2_Memo", 200, "左", "", false);
            myGrid1.Init_Column("录入时间", "Tj2_LrDate", 150, "中", "yyyy-MM-dd HH:mm:ss", false);
            myGrid1.Init_Column("经手人", "Tj2_Jsr_Name", 100, "左", "", false);
            myGrid1.AllowSort = true;
            myGrid1.AllowAddNew = true;
        }
        private void BtnState()
        {
            BtnDelete.Enabled = true;
            BtnDeleteAll.Enabled = true;
            BtnNew.Enabled = true;
            BtnSave.Enabled = true;
            BtnClose.Enabled = true;
            ControlEnable(true);
        }
        private void ControlEnable(bool flag)
        {
            TxtName.Enabled = flag;
            CmbJd.Enabled = flag;
            TxtBm.Enabled = flag;
            TxtYljg.Enabled = flag;
            NumRs.Enabled = flag;
            DtpDate.Enabled = flag;
            TxtMemo.Enabled = flag;
            myGrid1.AllowAddNew = flag;
        }
        #endregion

        #region  显示函数
        protected override void Zb_Clear()
        {
            base.Insert = true;
            _mdlZdTjJh = new MdlZd_TjJh();
            ZbRow = ZbTable.NewRow();
            TxtCode.Text = _bllZdTjJh.MaxCode(10);
            TxtName.Text = "";
            CmbJd.SelectedIndex = 0;
            TxtBm.Text = "";
            TxtYljg.Text = "";
            NumRs.Value = 0;
            DtpDate.Value = DateTime.Now;
            TxtMemo.Text = "";
            TxtName.Select();
        }

        private void Zb_Show()
        {
            _mdlZdTjJh = _bllZdTjJh.GetModel(ZbRow["Tj1_Code"] + "");
            TxtCode.Text = _mdlZdTjJh.Tj1_Code;
            TxtName.Text = _mdlZdTjJh.Tj1_Name;
            // 根据数据库值设置枚举选择
            switch (_mdlZdTjJh.Tj1_Jd)
            {
                case "第一季度":
                    CmbJd.SelectedIndex = 0;
                    break;
                case "第二季度":
                    CmbJd.SelectedIndex = 1;
                    break;
                case "第三季度":
                    CmbJd.SelectedIndex = 2;
                    break;
                case "第四季度":
                    CmbJd.SelectedIndex = 3;
                    break;
                default:
                    CmbJd.SelectedIndex = 0;
                    break;
            }
            TxtBm.Text = _mdlZdTjJh.Tj1_Bm;
            TxtYljg.Text = _mdlZdTjJh.Tj1_Yljg;
            NumRs.Value = _mdlZdTjJh.Tj1_Rs ?? 0;
            DtpDate.Value = _mdlZdTjJh.Tj1_Date ?? DateTime.Now;
            TxtMemo.Text = _mdlZdTjJh.Tj1_Memo;
            TxtName.Select();
        }
        private void Cb_Show()
        {
            MyTable = _bllZdTjJl.GetList($"Tj1_Code='{_mdlZdTjJh.Tj1_Code}'").Tables[0];
            MyTable.TableName = "明细";
            //主表记录
            MyCm = (CurrencyManager)BindingContext[MyTable];
            myGrid1.BeginInvoke(new Action<DataTable>(p =>
            {
                myGrid1.DataTable = p;
                this.LblTotal.Text = "∑=" + p.Rows.Count;
            }), MyTable);
            DataSum("");
        }
        #endregion

        #region 检查语句
        //检查主表数据
        private bool ZbCheck()
        {
            if (!this.Insert && _bllZdTjJh.GetRecordCount("Tj1_Code='" + TxtCode.Text + "'") == 0)
            {
                MessageBox.Show("此体检计划已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (TxtName.Text.Trim() == "")
            {
                MessageBox.Show("体检计划名称不能为空!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                TxtName.Focus();
                return false;
            }
            if (CustomControl.Func.NotAllowEmpty(DtpDate)) return false;
            if ((DateTime)DtpDate.Value > Convert.ToDateTime("2079-06-01"))
            {
                DtpDate.Select();
                MessageBox.Show("填写的制定计划时间超出范围，请重新输入!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            return true;
        }
        private bool ZbStateCheck(string state)
        {
            if (state == "从表修改")
            {
                // 从表修改时的检查逻辑
            }
            if (state == "删除")
            {
                if (!this.Insert && _bllZdTjJh.GetRecordCount("Tj1_Code='" + TxtCode.Text + "'") == 0)
                {
                    MessageBox.Show("此体检计划已经被删除，无法继续操作!请点击新单，重新录入", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    return false;
                }
            }
            return true;
        }
        #endregion

        #region 按钮函数
        //删除行
        protected override bool DataDeleteOne()
        {
            if (myGrid1.Row >= myGrid1.RowCount)
            {
                MessageBox.Show("请选择一条记录!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
            if (base.DataDeleteOne() == true)
            {
                _bllZdTjJl.Delete(base.SubItemRow["Tj2_Code"] + "");
                MyTable.AcceptChanges();
                DataSum("");
                LblTotal.Text = "∑=" + (MyTable.Rows.Count).ToString();
                return true;
            }
            return false;
        }

        protected override bool DataDeleteAll(string PrimaryKey, Func<string, bool> Delete)
        {

            if (_mdlZdTjJh == null)
            {
                MessageBox.Show("数据尚未保存,无法删除!", "警告", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }
            if (ZbStateCheck("删除") == false) return false;
            ZbRow.Delete();
            return base.DataDeleteAll(PrimaryKey, Delete);
        }

        protected override void DataNew()
        {
            myGrid1.UpdateData();
            if (base.MyTable.DataSet.HasChanges() == true)
            {
                if (MessageBox.Show("数据尚未保存,是否保存数据?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.OK)
                {
                    DataSave(true);
                }
            }

            Zb_Clear();
            BtnState();
            Cb_Show();
            TxtName.Select();
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="showMsgbox">是否弹出提示框</param>
        /// <returns></returns>
        protected override bool DataSave(bool showMsgbox)
        {
            if (ZbCheck() == false) return false;
            if (base.Insert == true) TxtCode.Text = _bllZdTjJh.MaxCode(10);
            DataSum("");
            if (base.Insert == true)
            {
                //增加记录
                Zb_Add();
            }
            else
            {
                //编辑记录
                Zb_Edit();
            }
            if (showMsgbox == true) MessageBox.Show("数据保存成功!", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return true;
        }

        #region 数据操作函数

        protected override void DataSum(string s)
        {
            if (base.BaseLblTotal != null)
            {
                this.BeginInvoke(new Action(() =>
                {
                    base.BaseLblTotal.Text = "∑=" + base.MyTable.Rows.Count.ToString();
                }));
            }
        }

        #region 主表

        //增加记录
        private void Zb_Add()
        {
            _mdlZdTjJh.Tj1_Code = _bllZdTjJh.MaxCode(10);
            TxtCode.Text = _mdlZdTjJh.Tj1_Code;
            _mdlZdTjJh.Tj1_Name = TxtName.Text.Trim();
            _mdlZdTjJh.Tj1_Jd = CmbJd.SelectedItem.ToString();
            _mdlZdTjJh.Tj1_Bm = TxtBm.Text.Trim();
            _mdlZdTjJh.Tj1_Yljg = TxtYljg.Text.Trim();
            _mdlZdTjJh.Tj1_Rs = (int)NumRs.Value;
            _mdlZdTjJh.Tj1_Date = (DateTime)DtpDate.Value;
            _mdlZdTjJh.Tj1_Memo = TxtMemo.Text.Trim();
            _mdlZdTjJh.Jsr_Code = YdVar.Var.JsrCode;
            _mdlZdTjJh.Jsr_Name = YdVar.Var.UserName;
            _mdlZdTjJh.Lr_Date = DateTime.Now;
            _bllZdTjJh.Add(_mdlZdTjJh);
            Common.DataTableToList.ToDataRow<MdlZd_TjJh>(_mdlZdTjJh, ZbRow);
            ZbTable.Rows.Add(ZbRow);
            base.Insert = false;
        }
        //编辑记录
        private void Zb_Edit()
        {
            _mdlZdTjJh.Tj1_Name = TxtName.Text.Trim();
            _mdlZdTjJh.Tj1_Jd = CmbJd.SelectedItem.ToString();
            _mdlZdTjJh.Tj1_Bm = TxtBm.Text.Trim();
            _mdlZdTjJh.Tj1_Yljg = TxtYljg.Text.Trim();
            _mdlZdTjJh.Tj1_Rs = (int)NumRs.Value;
            _mdlZdTjJh.Tj1_Date = (DateTime)DtpDate.Value;
            _mdlZdTjJh.Tj1_Memo = TxtMemo.Text.Trim();
            _bllZdTjJh.Update(_mdlZdTjJh);
            Common.DataTableToList.ToDataRow<MdlZd_TjJh>(_mdlZdTjJh, ZbRow);
        }
        #endregion

        #region 从表

        protected override void SubDataEdit()
        {
            if (ZbCheck() == false) return;
            if (ZbStateCheck("从表修改") == false) return;
            DataSave(false);
            bool subInsert;
            if ((myGrid1.Row + 1) > myGrid1.RowCount)
            {
                base.SubItemRow = base.MyTable.NewRow();
                subInsert = true;
            }
            else
            {
                base.SubItemRow = ((DataRowView)base.MyCm.List[myGrid1.Row]).Row;
                subInsert = false;
            }

            Zd_TjJh3 f = new Zd_TjJh3(_mdlZdTjJh.Tj1_Code, subInsert, base.SubItemRow, base.MyTable);
            // f.MyTransmitDataRow = this.MyTransmitDataRow;
            f.MyTransmitTxt = this.MyTransmitTxt;
            f.Owner = this;
            f.ShowDialog();

        }

        #endregion

        #endregion

        #endregion

        #endregion

        #region 控件动作

        #region 按钮动作

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            DataDeleteOne();
        }
        private void BtnDeleteAll_Click(object sender, EventArgs e)
        {
            DataDeleteAll(_mdlZdTjJh.Tj1_Code, _bllZdTjJh.Delete);
        }
        private void BtnNew_Click(object sender, EventArgs e)
        {
            DataNew();
        }
        private void BtnSave_Click(object sender, EventArgs e)
        {
            DataSave(true);
        }
        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        #endregion

        #region Grid动作
        private void myGrid1_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    this.SubDataEdit();
                    break;
            }
        }
        #endregion

        #endregion


    }
}
